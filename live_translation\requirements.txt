# --- START OF UPDATED FILE: requirements.txt ---

# --- Core Web Framework & Server ---
fastapi
uvicorn[standard]

# --- Configuration & Environment ---
python-dotenv

# --- AI & External Service Libraries ---
# OpenAI for STT and Translation
openai

# A modern, asynchronous HTTP client (still useful, good to keep)
httpx

# --- CHANGE: Google Cloud TTS library ---
google-cloud-texttospeech

# For audio processing (if you use advanced features in audio_processor)
# numpy # Not strictly required by the current code but can be useful

# For reading audio from the test client
# PyAudio is needed for test_client.py, not the server itself
# pyaudio

# For handling WebSocket connections in the test client
websocket-client