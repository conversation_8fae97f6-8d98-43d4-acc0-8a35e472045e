import asyncio
import logging
import io
import wave
import audioop
from typing import Optional, List, Tuple
import collections

# Try to import optional dependencies
try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    np = None

try:
    import webrtcvad
    VAD_AVAILABLE = True
except ImportError:
    VAD_AVAILABLE = False
    webrtcvad = None

logger = logging.getLogger(__name__)

class AudioProcessor:
    """Handles real-time audio processing for translation"""
    
    def __init__(self):
        # Audio configuration
        self.sample_rate = 16000  # 16kHz for speech recognition
        self.frame_duration_ms = 30  # 30ms frames
        self.frame_size = int(self.sample_rate * self.frame_duration_ms / 1000)
        
        # Voice Activity Detection - More aggressive settings to reduce false positives
        if VAD_AVAILABLE:
            self.vad = webrtcvad.Vad(3)  # Aggressiveness level 3 (most aggressive) to reduce noise detection
        else:
            self.vad = None
            logger.warning("WebRTC VAD not available, using simple audio detection")

        # Audio buffering
        self.audio_buffers = {}  # session_id -> audio buffer
        self.silence_buffers = {}  # session_id -> silence buffer

        # VAD parameters - Optimized for real speech detection while filtering noise
        self.silence_threshold = 1.0  # seconds of silence before processing
        self.min_speech_duration = 0.8  # minimum speech duration to process
        self.min_speech_energy = 800  # minimum RMS energy for speech detection (more sensitive)
        self.min_consecutive_speech_frames = 2  # minimum consecutive speech frames needed (very responsive)

        # Audio accumulation for better processing
        self.speech_accumulators = {}  # session_id -> accumulated speech data
        self.last_speech_time = {}  # session_id -> last time speech was detected
        self.consecutive_silence_frames = {}  # session_id -> count of consecutive silence frames
        self.consecutive_speech_frames = {}  # session_id -> count of consecutive speech frames
        self.last_valid_speech_time = {}  # session_id -> last time we had confirmed valid speech
        
    def initialize_session(self, session_id: str):
        """Initialize audio processing for a session"""
        self.audio_buffers[session_id] = collections.deque(maxlen=100)  # Keep last 100 frames
        self.silence_buffers[session_id] = collections.deque(maxlen=20)  # Keep last 20 silence frames
        self.speech_accumulators[session_id] = b''  # Accumulated speech data
        self.last_speech_time[session_id] = 0  # Last speech detection time
        self.consecutive_silence_frames[session_id] = 0  # Track consecutive silence
        self.consecutive_speech_frames[session_id] = 0  # Track consecutive speech
        self.last_valid_speech_time[session_id] = 0  # Track last confirmed valid speech
        logger.info(f"Audio processing initialized for session {session_id}")
    
    def cleanup_session(self, session_id: str):
        """Clean up audio processing for a session"""
        if session_id in self.audio_buffers:
            del self.audio_buffers[session_id]
        if session_id in self.silence_buffers:
            del self.silence_buffers[session_id]
        if session_id in self.speech_accumulators:
            del self.speech_accumulators[session_id]
        if session_id in self.last_speech_time:
            del self.last_speech_time[session_id]
        if session_id in self.consecutive_silence_frames:
            del self.consecutive_silence_frames[session_id]
        if session_id in self.consecutive_speech_frames:
            del self.consecutive_speech_frames[session_id]
        if session_id in self.last_valid_speech_time:
            del self.last_valid_speech_time[session_id]
        logger.info(f"Audio processing cleaned up for session {session_id}")
    
    async def process_audio_chunk(self, session_id: str, audio_data: bytes) -> Optional[bytes]:
        """Process incoming audio chunk and return processed audio if speech is detected"""
        try:
            if session_id not in self.audio_buffers:
                self.initialize_session(session_id)

            # Convert audio data to the required format
            processed_audio = await self._preprocess_audio(audio_data)

            if not processed_audio:
                return None

            # Detect voice activity with improved logic
            has_speech = await self._detect_speech_in_chunk(processed_audio)

            current_time = asyncio.get_event_loop().time()

            if has_speech:
                # Increment consecutive speech frames and reset silence counter
                self.consecutive_speech_frames[session_id] += 1
                self.consecutive_silence_frames[session_id] = 0
                self.last_speech_time[session_id] = current_time

                # Only start accumulating after we have enough consecutive speech frames
                if self.consecutive_speech_frames[session_id] >= self.min_consecutive_speech_frames:
                    self.speech_accumulators[session_id] += processed_audio
                    logger.debug(f"Valid speech detected, accumulated {len(self.speech_accumulators[session_id])} bytes")

                    # Check if we have enough speech data (at least 2 seconds for good quality)
                    min_speech_bytes = self.sample_rate * 2 * 2  # 2 seconds of 16-bit audio
                    if len(self.speech_accumulators[session_id]) >= min_speech_bytes:
                        # Validate the accumulated speech has good quality
                        if self._validate_speech_quality(self.speech_accumulators[session_id], strict=False):
                            # Extract accumulated speech for processing
                            speech_data = self.speech_accumulators[session_id]
                            self.speech_accumulators[session_id] = b''  # Reset accumulator
                            self.last_valid_speech_time[session_id] = current_time

                            # Create WAV format for STT
                            wav_data = self._create_wav_data(speech_data)
                            logger.info(f"🎤 Extracted {len(speech_data)} bytes of validated speech for STT processing")
                            return wav_data
                        else:
                            # Clear low-quality accumulated data
                            logger.debug("Clearing low-quality accumulated speech data")
                            self.speech_accumulators[session_id] = b''
                            self.consecutive_speech_frames[session_id] = 0
                else:
                    logger.debug(f"Waiting for more consecutive speech frames: {self.consecutive_speech_frames[session_id]}/{self.min_consecutive_speech_frames}")
            else:
                # Reset consecutive speech frames and increment silence counter
                self.consecutive_speech_frames[session_id] = 0
                self.consecutive_silence_frames[session_id] += 1

                # Check if we should process accumulated speech due to sufficient silence
                time_since_speech = current_time - self.last_speech_time[session_id]
                time_since_valid_speech = current_time - self.last_valid_speech_time[session_id]
                silence_frames_threshold = int(self.silence_threshold * 1000 / self.frame_duration_ms)  # Convert to frame count

                # Only process if we had recent valid speech and sufficient silence
                if (time_since_speech > self.silence_threshold and
                    time_since_valid_speech < 10.0 and  # Only if we had valid speech in last 10 seconds
                    self.consecutive_silence_frames[session_id] >= silence_frames_threshold and
                    len(self.speech_accumulators[session_id]) > 0):

                    # Only process if we have minimum speech duration and good quality
                    min_speech_bytes = int(self.sample_rate * 2 * self.min_speech_duration)
                    if (len(self.speech_accumulators[session_id]) >= min_speech_bytes and
                        self._validate_speech_quality(self.speech_accumulators[session_id], strict=False)):

                        speech_data = self.speech_accumulators[session_id]
                        self.speech_accumulators[session_id] = b''  # Reset accumulator
                        self.consecutive_silence_frames[session_id] = 0  # Reset silence counter
                        self.consecutive_speech_frames[session_id] = 0  # Reset speech counter

                        wav_data = self._create_wav_data(speech_data)
                        logger.info(f"🔇 Silence detected, processing {len(speech_data)} bytes of validated accumulated speech")
                        return wav_data
                    else:
                        # Clear insufficient or low-quality data
                        logger.debug("Clearing insufficient or low-quality speech data due to silence")
                        self.speech_accumulators[session_id] = b''
                        self.consecutive_silence_frames[session_id] = 0
                        self.consecutive_speech_frames[session_id] = 0

            return None

        except Exception as e:
            logger.error(f"Error processing audio chunk for session {session_id}: {str(e)}")
            return None
    
    async def _preprocess_audio(self, audio_data: bytes) -> Optional[bytes]:
        """Preprocess audio data to required format with quality checks"""
        try:
            # Frontend sends raw PCM Int16Array buffer (16-bit, 16kHz, mono)
            # We need to validate and ensure it's in the correct format

            # Check if we have enough data (at least 10ms of audio for better quality)
            min_bytes = int(self.sample_rate * 0.01 * 2)  # 10ms at 16kHz, 16-bit
            if len(audio_data) < min_bytes:
                logger.debug(f"Audio chunk too small: {len(audio_data)} bytes")
                return None

            # Ensure the data length is even (16-bit samples)
            if len(audio_data) % 2 != 0:
                logger.debug("Odd number of bytes, truncating last byte")
                audio_data = audio_data[:-1]

            # Basic quality check - ensure audio isn't just silence or noise
            if not self._has_meaningful_audio(audio_data):
                logger.debug("Audio chunk contains only silence or noise")
                return None

            logger.debug(f"Preprocessed audio: {len(audio_data)} bytes")
            return audio_data

        except Exception as e:
            logger.error(f"Error preprocessing audio: {str(e)}")
            return None

    def _has_meaningful_audio(self, audio_data: bytes) -> bool:
        """Check if audio data contains meaningful content (not just silence/noise)"""
        try:
            if len(audio_data) < 4:
                return False

            # Convert to samples
            samples = []
            for i in range(0, len(audio_data), 2):
                if i + 1 < len(audio_data):
                    sample = int.from_bytes(audio_data[i:i+2], byteorder='little', signed=True)
                    samples.append(sample)

            if not samples:
                return False

            # Calculate basic statistics
            max_amplitude = max(abs(s) for s in samples)
            rms = (sum(s * s for s in samples) / len(samples)) ** 0.5

            # Reasonable checks for meaningful audio - responsive to real speech
            min_amplitude_threshold = 200  # Sensitive to real speech
            min_rms_threshold = 50  # Low threshold for quiet speech
            min_dynamic_range = 100  # Allow for quieter speech patterns

            dynamic_range = max_amplitude - min(abs(s) for s in samples)

            has_amplitude = max_amplitude > min_amplitude_threshold
            has_energy = rms > min_rms_threshold
            has_variation = dynamic_range > min_dynamic_range

            is_meaningful = has_amplitude and has_energy and has_variation

            if not is_meaningful:
                logger.debug(f"Audio not meaningful: Peak={max_amplitude}, RMS={rms:.1f}, Range={dynamic_range}")

            return is_meaningful

        except Exception as e:
            logger.debug(f"Error checking meaningful audio: {e}")
            return True  # Assume meaningful if check fails

    async def _detect_speech_in_chunk(self, audio_data: bytes) -> bool:
        """Detect if there's speech in the audio chunk"""
        try:
            if not audio_data or len(audio_data) < 2:
                return False

            # Use WebRTC VAD if available
            if self.vad:
                # WebRTC VAD requires specific frame sizes (10ms, 20ms, or 30ms)
                frame_size_bytes = self.frame_size * 2  # 30ms frame in bytes

                # Process the chunk in VAD-compatible frames
                speech_detected = False
                for i in range(0, len(audio_data), frame_size_bytes):
                    frame = audio_data[i:i + frame_size_bytes]

                    # Pad frame if necessary
                    if len(frame) < frame_size_bytes:
                        frame += b'\x00' * (frame_size_bytes - len(frame))

                    try:
                        if self.vad.is_speech(frame, self.sample_rate):
                            speech_detected = True
                            break
                    except Exception as vad_error:
                        logger.debug(f"VAD error: {vad_error}")
                        # Fall back to energy detection
                        if self._simple_voice_detection(frame):
                            speech_detected = True
                            break

                return speech_detected
            else:
                # Use simple energy-based detection
                return self._simple_voice_detection(audio_data)

        except Exception as e:
            logger.error(f"Error in speech detection: {str(e)}")
            return True  # Assume speech if detection fails
    
    async def _detect_voice_activity(self, audio_data: bytes) -> Tuple[List[bytes], List[bytes]]:
        """Detect voice activity in audio data"""
        try:
            speech_frames = []
            silence_frames = []
            
            # Convert audio to 16-bit PCM if needed
            pcm_data = await self._convert_to_pcm(audio_data)
            
            if not pcm_data:
                return speech_frames, silence_frames
            
            # Split into frames for VAD
            frame_size_bytes = self.frame_size * 2  # 16-bit = 2 bytes per sample
            
            for i in range(0, len(pcm_data), frame_size_bytes):
                frame = pcm_data[i:i + frame_size_bytes]
                
                if len(frame) < frame_size_bytes:
                    # Pad the last frame if necessary
                    frame += b'\x00' * (frame_size_bytes - len(frame))
                
                try:
                    # Check if frame contains speech
                    if self.vad:
                        is_speech = self.vad.is_speech(frame, self.sample_rate)
                    else:
                        # Simple energy-based detection if VAD not available
                        is_speech = self._simple_voice_detection(frame)

                    if is_speech:
                        speech_frames.append(frame)
                    else:
                        silence_frames.append(frame)

                except Exception as vad_error:
                    # If VAD fails, assume it's speech
                    logger.debug(f"VAD error, assuming speech: {str(vad_error)}")
                    speech_frames.append(frame)
            
            logger.debug(f"VAD detected {len(speech_frames)} speech frames, {len(silence_frames)} silence frames")
            return speech_frames, silence_frames
            
        except Exception as e:
            logger.error(f"Error in voice activity detection: {str(e)}")
            return [], []
    
    async def _convert_to_pcm(self, audio_data: bytes) -> Optional[bytes]:
        """Convert audio data to 16-bit PCM format"""
        try:
            # Frontend sends raw PCM Int16Array buffer, so it's already in PCM format
            # Just validate the format is correct

            # Check if data length is appropriate for 16-bit samples
            if len(audio_data) % 2 != 0:
                logger.warning("Audio data length not aligned to 16-bit samples")
                audio_data = audio_data[:-1]  # Remove last byte

            # The data is already 16-bit PCM at 16kHz mono from frontend
            logger.debug(f"Audio data is already PCM format: {len(audio_data)} bytes")
            return audio_data

        except Exception as e:
            logger.error(f"Error converting audio to PCM: {str(e)}")
            return None
    
    def _is_pcm_format(self, audio_data: bytes) -> bool:
        """Check if audio data is in PCM format"""
        try:
            # Check for WAV header
            if len(audio_data) > 44 and audio_data[:4] == b'RIFF':
                return True

            # Frontend sends raw PCM Int16Array buffer without headers
            # Check if data length is reasonable for PCM (even number of bytes for 16-bit)
            if len(audio_data) % 2 == 0 and len(audio_data) > 0:
                return True

            return False

        except Exception:
            return False

    def _simple_voice_detection(self, audio_data: bytes) -> bool:
        """Simple energy-based voice detection when VAD is not available"""
        try:
            # Convert bytes to integers
            if len(audio_data) < 2:
                return False

            # Calculate RMS energy
            samples = []
            for i in range(0, len(audio_data), 2):
                if i + 1 < len(audio_data):
                    sample = int.from_bytes(audio_data[i:i+2], byteorder='little', signed=True)
                    samples.append(sample)

            if not samples:
                return False

            # Calculate RMS
            rms = (sum(s * s for s in samples) / len(samples)) ** 0.5

            # Balanced threshold to reduce false positives while detecting real speech
            threshold = self.min_speech_energy  # Use configurable threshold

            # Additional checks for real speech
            max_amplitude = max(abs(s) for s in samples)
            dynamic_range = max_amplitude - min(abs(s) for s in samples)

            # Speech should have sufficient energy OR dynamic range (more flexible)
            has_energy = rms > threshold
            has_dynamics = dynamic_range > 200  # Allow for quieter speech
            has_peak = max_amplitude > 500  # Lower peak requirement for real speech

            is_speech = has_energy and has_dynamics and has_peak

            if is_speech:
                logger.debug(f"Speech detected: RMS={rms:.1f}, Peak={max_amplitude}, Range={dynamic_range}")
            else:
                logger.debug(f"No speech: RMS={rms:.1f}, Peak={max_amplitude}, Range={dynamic_range} "
                           f"(Energy={has_energy}, Dynamics={has_dynamics}, Peak={has_peak})")

            return is_speech

        except Exception as e:
            logger.debug(f"Simple voice detection error: {e}")
            return False  # Assume no speech if detection fails (changed from True)

    def _validate_speech_quality(self, audio_data: bytes, strict: bool = False) -> bool:
        """Validate that accumulated audio data contains sufficient speech quality"""
        try:
            if len(audio_data) < 2:
                return False

            # Calculate overall RMS energy
            samples = []
            for i in range(0, len(audio_data), 2):
                if i + 1 < len(audio_data):
                    sample = int.from_bytes(audio_data[i:i+2], byteorder='little', signed=True)
                    samples.append(sample)

            if not samples:
                return False

            # Calculate RMS
            rms = (sum(s * s for s in samples) / len(samples)) ** 0.5

            # Set thresholds based on strictness
            if strict:
                min_overall_energy = self.min_speech_energy * 1.2  # 20% higher for strict mode
                min_dynamic_range = 1200  # Higher variation required
                min_peak_amplitude = 4000  # Minimum peak amplitude for real speech
            else:
                min_overall_energy = self.min_speech_energy * 0.4  # Very lenient for normal mode
                min_dynamic_range = 200  # Low variation required
                min_peak_amplitude = 400  # Low peak amplitude required

            # Calculate various quality metrics
            max_sample = max(abs(s) for s in samples)
            min_sample = min(abs(s) for s in samples)
            dynamic_range = max_sample - min_sample

            # Calculate signal-to-noise ratio approximation
            sorted_samples = sorted([abs(s) for s in samples])
            noise_floor = sum(sorted_samples[:len(sorted_samples)//10]) / (len(sorted_samples)//10)  # Bottom 10%
            signal_peak = sum(sorted_samples[-len(sorted_samples)//10:]) / (len(sorted_samples)//10)  # Top 10%
            snr_approx = signal_peak / max(noise_floor, 1)

            # Quality checks
            has_sufficient_energy = rms > min_overall_energy
            has_dynamic_range = dynamic_range > min_dynamic_range
            has_peak_amplitude = max_sample > min_peak_amplitude
            has_good_snr = snr_approx > (15 if strict else 8)  # Signal-to-noise ratio

            # Additional check for speech-like patterns (energy distribution)
            energy_variance = sum((abs(s) - rms) ** 2 for s in samples) / len(samples)
            has_speech_patterns = energy_variance > (rms * rms * 0.5)  # Speech has energy variation

            if strict:
                is_valid = (has_sufficient_energy and has_dynamic_range and
                           has_peak_amplitude and has_good_snr and has_speech_patterns)
            else:
                is_valid = has_sufficient_energy and has_dynamic_range and has_peak_amplitude

            if not is_valid:
                logger.debug(f"Speech validation {'(STRICT)' if strict else ''} failed: "
                           f"RMS={rms:.1f}, Dynamic Range={dynamic_range}, Peak={max_sample}, "
                           f"SNR≈{snr_approx:.1f}, Energy OK={has_sufficient_energy}, "
                           f"Range OK={has_dynamic_range}, Peak OK={has_peak_amplitude}, "
                           f"SNR OK={has_good_snr}, Patterns OK={has_speech_patterns}")
            else:
                logger.debug(f"Speech validation {'(STRICT)' if strict else ''} passed: "
                           f"RMS={rms:.1f}, Dynamic Range={dynamic_range}, Peak={max_sample}, SNR≈{snr_approx:.1f}")

            return is_valid

        except Exception as e:
            logger.debug(f"Speech validation error: {e}")
            return False

    def _get_min_frames_for_processing(self) -> int:
        """Get minimum number of frames needed for processing"""
        # Minimum 1 second of audio at 30ms frames = 33 frames
        return int(1000 / self.frame_duration_ms)
    
    def _extract_audio_for_processing(self, session_id: str) -> Optional[bytes]:
        """Extract audio data from buffer for processing"""
        try:
            if session_id not in self.audio_buffers:
                return None
            
            buffer = self.audio_buffers[session_id]
            
            if len(buffer) < self._get_min_frames_for_processing():
                return None
            
            # Extract all buffered frames
            frames = list(buffer)
            buffer.clear()  # Clear buffer after extraction
            
            # Combine frames into single audio data
            combined_audio = b''.join(frames)
            
            # Create WAV format for speech recognition
            wav_data = self._create_wav_data(combined_audio)
            
            return wav_data
            
        except Exception as e:
            logger.error(f"Error extracting audio for processing: {str(e)}")
            return None
    
    def _create_wav_data(self, pcm_data: bytes) -> bytes:
        """Create WAV format audio data from PCM"""
        try:
            # Create WAV file in memory
            wav_buffer = io.BytesIO()
            
            with wave.open(wav_buffer, 'wb') as wav_file:
                wav_file.setnchannels(1)  # Mono
                wav_file.setsampwidth(2)  # 16-bit
                wav_file.setframerate(self.sample_rate)  # 16kHz
                wav_file.writeframes(pcm_data)
            
            wav_buffer.seek(0)
            wav_data = wav_buffer.read()
            wav_buffer.close()
            
            return wav_data
            
        except Exception as e:
            logger.error(f"Error creating WAV data: {str(e)}")
            return pcm_data  # Return raw PCM as fallback
    
    def get_session_stats(self, session_id: str) -> dict:
        """Get audio processing statistics for a session"""
        try:
            if session_id not in self.audio_buffers:
                return {"error": "Session not found"}
            
            buffer = self.audio_buffers[session_id]
            silence_buffer = self.silence_buffers.get(session_id, collections.deque())
            
            return {
                "buffered_frames": len(buffer),
                "silence_frames": len(silence_buffer),
                "buffer_duration_ms": len(buffer) * self.frame_duration_ms,
                "sample_rate": self.sample_rate,
                "frame_duration_ms": self.frame_duration_ms
            }
            
        except Exception as e:
            logger.error(f"Error getting session stats: {str(e)}")
            return {"error": str(e)}
    
    async def enhance_audio_quality(self, audio_data: bytes) -> bytes:
        """Enhance audio quality for better speech recognition"""
        try:
            # Apply noise reduction and normalization
            # This is a placeholder for audio enhancement
            
            # In production, you might use:
            # - Noise reduction algorithms
            # - Audio normalization
            # - Echo cancellation
            # - Bandwidth extension
            
            return audio_data
            
        except Exception as e:
            logger.error(f"Error enhancing audio quality: {str(e)}")
            return audio_data
    
    def adjust_audio_levels(self, audio_data: bytes, target_level: float = 0.7) -> bytes:
        """Adjust audio levels for optimal processing"""
        try:
            if not NUMPY_AVAILABLE:
                logger.debug("NumPy not available, skipping audio level adjustment")
                return audio_data

            # Convert to numpy array for processing
            audio_array = np.frombuffer(audio_data, dtype=np.int16)

            # Calculate current RMS level
            rms = np.sqrt(np.mean(audio_array.astype(np.float32) ** 2))

            if rms > 0:
                # Calculate scaling factor
                target_rms = target_level * 32767  # 16-bit max value
                scale_factor = target_rms / rms

                # Apply scaling
                scaled_audio = audio_array * scale_factor

                # Clip to prevent overflow
                scaled_audio = np.clip(scaled_audio, -32767, 32767)

                return scaled_audio.astype(np.int16).tobytes()

            return audio_data

        except Exception as e:
            logger.error(f"Error adjusting audio levels: {str(e)}")
            return audio_data
